<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="1.0" toolsVersion="1938" systemVersion="11C74" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" initialViewController="Gcm-Eb-clI">
    <dependencies>
        <development defaultVersion="4200" identifier="xcode"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="933"/>
    </dependencies>
    <scenes>
        <scene sceneID="EAU-5a-w1q">
            <objects>
                <placeholder placeholderIdentifier="IBFirstResponder" id="aXs-Pw-3nx" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <viewController id="w8n-Lo-Phj" customClass="ImageViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="XIt-Ms-Asu">
                        <rect key="frame" x="0.0" y="64" width="320" height="416"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <imageView multipleTouchEnabled="YES" contentMode="scaleToFill" image="sample.jpg" id="vCo-Ce-GJo">
                                <rect key="frame" x="0.0" y="1" width="320" height="416"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                    </view>
                    <navigationItem key="navigationItem" title="Image" id="bKj-7i-Clc">
                        <barButtonItem key="leftBarButtonItem" title="Take photo" id="Gye-v4-zLL">
                            <connections>
                                <action selector="takePhoto:" destination="w8n-Lo-Phj" id="Qxh-Hw-9QF"/>
                            </connections>
                        </barButtonItem>
                        <barButtonItem key="rightBarButtonItem" title="Recognize" id="veB-M5-RE3">
                            <connections>
                                <segue destination="mxe-f6-qGS" kind="push" id="sh6-jw-xf7"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="imageView" destination="vCo-Ce-GJo" id="ZfN-8D-0PN"/>
                    </connections>
                </viewController>
            </objects>
            <point key="canvasLocation" x="749" y="63"/>
        </scene>
        <scene sceneID="iWq-r4-Mjf">
            <objects>
                <placeholder placeholderIdentifier="IBFirstResponder" id="EPb-jO-XH4" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <viewController id="mxe-f6-qGS" customClass="RecognitionViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="msE-A5-vhU">
                        <rect key="frame" x="0.0" y="64" width="320" height="416"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" id="3np-ch-e2E">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="416"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                <string key="text">Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda.</string>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                            </textView>
                            <activityIndicatorView opaque="NO" contentMode="scaleToFill" animating="YES" style="gray" id="9wq-xL-8Fw">
                                <rect key="frame" x="150" y="198" width="20" height="20"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            </activityIndicatorView>
                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" text="Loading image..." textAlignment="center" lineBreakMode="tailTruncation" minimumFontSize="10" id="CQy-LS-Y97">
                                <rect key="frame" x="50" y="169" width="220" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                    </view>
                    <navigationItem key="navigationItem" title="Recognized Text" id="t7j-sR-tfo"/>
                    <connections>
                        <outlet property="statusIndicator" destination="9wq-xL-8Fw" id="6qx-xk-2bw"/>
                        <outlet property="statusLabel" destination="CQy-LS-Y97" id="pPN-Yg-ohJ"/>
                        <outlet property="textView" destination="3np-ch-e2E" id="NpC-nn-ghj"/>
                    </connections>
                </viewController>
            </objects>
            <point key="canvasLocation" x="489" y="685"/>
        </scene>
        <scene sceneID="WyD-he-x59">
            <objects>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Npj-wh-Ltl" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <navigationController definesPresentationContext="YES" id="Gcm-Eb-clI" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" id="OXj-yN-Da8">
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="w8n-Lo-Phj" kind="relationship" relationship="rootViewController" id="m0W-gc-gmq"/>
                    </connections>
                </navigationController>
            </objects>
            <point key="canvasLocation" x="215" y="63"/>
        </scene>
    </scenes>
    <resources>
        <image name="sample.jpg" width="620" height="752"/>
    </resources>
    <classes>
        <class className="ImageViewController" superclassName="UIViewController">
            <source key="sourceIdentifier" type="project" relativePath="./Classes/ImageViewController.h"/>
            <relationships>
                <relationship kind="action" name="takePhoto:"/>
                <relationship kind="outlet" name="imageView" candidateClass="UIImageView"/>
            </relationships>
        </class>
        <class className="RecognitionViewController" superclassName="UIViewController">
            <source key="sourceIdentifier" type="project" relativePath="./Classes/RecognitionViewController.h"/>
            <relationships>
                <relationship kind="outlet" name="statusIndicator" candidateClass="UIActivityIndicatorView"/>
                <relationship kind="outlet" name="statusLabel" candidateClass="UILabel"/>
                <relationship kind="outlet" name="textView" candidateClass="UITextView"/>
            </relationships>
        </class>
    </classes>
    <simulatedMetricsContainer key="defaultSimulatedMetrics">
        <simulatedStatusBarMetrics key="statusBar"/>
        <simulatedOrientationMetrics key="orientation"/>
        <simulatedScreenMetrics key="destination"/>
    </simulatedMetricsContainer>
</document>