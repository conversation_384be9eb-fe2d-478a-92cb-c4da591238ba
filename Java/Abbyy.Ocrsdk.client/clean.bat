@echo off
REM ABBYY Cloud OCR SDK Java Client - Clean Script
REM This script cleans all compiled files and build artifacts

echo ========================================
echo ABBYY Cloud OCR SDK - Clean Script
echo ========================================

REM Check if <PERSON>ven is available
where mvn >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo WARNING: <PERSON>ven is not available, performing manual cleanup
    goto MANUAL_CLEAN
)

REM Use Maven clean
echo Using Maven to clean project...
mvn clean

if %ERRORLEVEL% equ 0 (
    echo.
    echo Maven clean completed successfully!
    goto ADDITIONAL_CLEAN
) else (
    echo Maven clean failed, performing manual cleanup...
    goto MANUAL_CLEAN
)

:MANUAL_CLEAN
echo Performing manual cleanup...

REM Remove target directory
if exist "target" (
    echo Removing target directory...
    rmdir /s /q "target"
    if exist "target" (
        echo WARNING: Could not remove target directory completely
    ) else (
        echo Target directory removed successfully
    )
)

REM Remove old bin directory if exists
if exist "bin" (
    echo Removing old bin directory...
    rmdir /s /q "bin"
    if exist "bin" (
        echo WARNING: Could not remove bin directory completely
    ) else (
        echo Bin directory removed successfully
    )
)

:ADDITIONAL_CLEAN
REM Remove any temporary files
echo Cleaning temporary files...
if exist "*.tmp" del /q "*.tmp"
if exist "*.log" del /q "*.log"

echo.
echo ========================================
echo Cleanup completed!
echo ========================================
echo.
echo All compiled files and build artifacts have been removed.
echo Run compile.bat to rebuild the project.
echo.

pause
