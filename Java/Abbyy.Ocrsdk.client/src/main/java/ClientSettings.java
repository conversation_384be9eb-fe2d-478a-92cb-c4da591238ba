import java.net.*;
import java.io.*;
import java.util.Properties;

/**
 * Authentication settings for Cloud OCR SDK client.
 *
 * Configuration is loaded in the following priority order:
 * 1. Environment variables (ABBYY_APP_ID, ABBYY_PASSWORD, ABBYY_SERVER_URL)
 * 2. config.properties file in classpath
 * 3. Default values (for backward compatibility)
 */
public class ClientSettings {
	// Default values for backward compatibility
	private static final String DEFAULT_APPLICATION_ID = "First Application";
	private static final String DEFAULT_PASSWORD = "rVeVXq3ji9V5OTH3HqddNE9h";
	private static final String DEFAULT_SERVER_URL = "https://cloud.ocrsdk.com";

	// Configuration properties
	private static Properties config = null;

	// Public configuration accessors
	public static final String APPLICATION_ID = getApplicationId();
	public static final String PASSWORD = getPassword();
	public static final String SERVER_URL = getServerUrl();

	/**
	 * Load configuration from environment variables, config.properties file, or use defaults
	 */
	private static Properties loadConfig() {
		if (config != null) {
			return config;
		}

		config = new Properties();

		// Try to load from config.properties file
		try (InputStream input = ClientSettings.class.getClassLoader().getResourceAsStream("config.properties")) {
			if (input != null) {
				config.load(input);
				System.out.println("Loaded configuration from config.properties");
			}
		} catch (IOException e) {
			System.out.println("Could not load config.properties: " + e.getMessage());
		}

		return config;
	}

	/**
	 * Get Application ID from environment variable, config file, or default
	 */
	private static String getApplicationId() {
		// 1. Check environment variable
		String envValue = System.getenv("ABBYY_APP_ID");
		if (envValue != null && !envValue.trim().isEmpty()) {
			return envValue.trim();
		}

		// 2. Check config file
		Properties props = loadConfig();
		String configValue = props.getProperty("abbyy.application.id");
		if (configValue != null && !configValue.trim().isEmpty()) {
			return configValue.trim();
		}

		// 3. Use default value
		return DEFAULT_APPLICATION_ID;
	}

	/**
	 * Get Password from environment variable, config file, or default
	 */
	private static String getPassword() {
		// 1. Check environment variable
		String envValue = System.getenv("ABBYY_PASSWORD");
		if (envValue != null && !envValue.trim().isEmpty()) {
			return envValue.trim();
		}

		// 2. Check config file
		Properties props = loadConfig();
		String configValue = props.getProperty("abbyy.password");
		if (configValue != null && !configValue.trim().isEmpty()) {
			return configValue.trim();
		}

		// 3. Use default value
		return DEFAULT_PASSWORD;
	}

	/**
	 * Get Server URL from environment variable, config file, or default
	 */
	private static String getServerUrl() {
		// 1. Check environment variable
		String envValue = System.getenv("ABBYY_SERVER_URL");
		if (envValue != null && !envValue.trim().isEmpty()) {
			return envValue.trim();
		}

		// 2. Check config file
		Properties props = loadConfig();
		String configValue = props.getProperty("abbyy.server.url");
		if (configValue != null && !configValue.trim().isEmpty()) {
			return configValue.trim();
		}

		// 3. Use default value
		return DEFAULT_SERVER_URL;
	}

	public static void setupProxy()
	{
		// Uncomment this if you are behind a proxy
		/*
		String host = "";
		String port = "";
		final String user = "";
		final String password = "";

		Authenticator.setDefault(
			new Authenticator() {
		 		public PasswordAuthentication getPasswordAuthentication() {
					return new PasswordAuthentication( user, password.toCharArray());
				}
 			}
		);

		System.getProperties().put("http.proxyHost", host );
		System.getProperties().put("https.proxyHost", host );
		System.getProperties().put("http.proxyPort", port);
		System.getProperties().put("https.proxyPort", port);
		System.getProperties().put("http.proxyUser", user);
		System.getProperties().put("https.proxyUser", user);
		System.getProperties().put("http.proxyPassword", password);
		System.getProperties().put("https.proxyPassword", password);
		*/
	}
}
