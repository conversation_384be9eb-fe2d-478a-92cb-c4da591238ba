@echo off
REM ABBYY Cloud OCR SDK Java Client - ProcessManyFiles Runner
REM This script runs the ProcessManyFiles batch processing program

setlocal enabledelayedexpansion

REM Check if compiled classes exist
if not exist "target\classes\ProcessManyFiles.class" (
    echo ERROR: Project is not compiled yet
    echo Please run compile.bat first
    echo.
    pause
    exit /b 1
)

REM Check if Java is available
where java >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java JDK and add it to your PATH
    pause
    exit /b 1
)

REM Show help if no arguments provided
if "%~1"=="" (
    echo ========================================
    echo ABBYY Cloud OCR SDK - ProcessManyFiles Runner
    echo ========================================
    echo.
    echo Usage: run-processmany.bat [mode] [arguments...]
    echo.
    echo Available modes:
    echo   recognize    - Recognize all images from a directory
    echo   remote       - Process remote images
    echo   help [mode]  - Show detailed help for a mode
    echo.
    echo Examples:
    echo   run-processmany.bat recognize images_folder output_folder
    echo   run-processmany.bat recognize --lang=French,Spanish images output
    echo   run-processmany.bat help recognize
    echo.
    echo Configuration:
    echo   Set environment variables ABBYY_APP_ID and ABBYY_PASSWORD
    echo   Or copy config.properties.template to config.properties and edit
    echo.
    pause
    exit /b 0
)

REM Run ProcessManyFiles with all arguments
echo Running ProcessManyFiles with arguments: %*
echo.
java -cp target\classes ProcessManyFiles %*

REM Check exit code
if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo ProcessManyFiles execution failed!
    echo ========================================
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ========================================
echo ProcessManyFiles execution completed successfully!
echo ========================================
