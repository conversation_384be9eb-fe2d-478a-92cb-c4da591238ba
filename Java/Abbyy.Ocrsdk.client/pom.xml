<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.abbyy</groupId>
    <artifactId>ocrsdk-client</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>ABBYY Cloud OCR SDK Java Client</name>
    <description>Java client library for ABBYY Cloud OCR SDK</description>
    <url>https://github.com/abbyy/ocrsdk.com</url>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
        <maven.exec.plugin.version>3.1.0</maven.exec.plugin.version>
    </properties>

    <build>
        <plugins>
            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArgs>
                        <arg>-Xlint:deprecation</arg>
                        <arg>-Xlint:unchecked</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- Maven Exec Plugin for running applications -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>${maven.exec.plugin.version}</version>
                <configuration>
                    <mainClass>TestApp</mainClass>
                </configuration>
                <executions>
                    <!-- TestApp execution -->
                    <execution>
                        <id>run-testapp</id>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>TestApp</mainClass>
                        </configuration>
                    </execution>
                    <!-- ProcessManyFiles execution -->
                    <execution>
                        <id>run-processmany</id>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>ProcessManyFiles</mainClass>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven JAR Plugin for creating executable JAR -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <mainClass>TestApp</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- No external dependencies - using only JDK standard libraries -->
    <dependencies>
        <!-- No dependencies required -->
    </dependencies>

</project>
