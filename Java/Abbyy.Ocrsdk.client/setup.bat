@echo off
REM ABBYY Cloud OCR SDK Java Client - Setup and Environment Check
REM This script checks the development environment and helps with initial setup

echo ========================================
echo ABBYY Cloud OCR SDK - Environment Setup
echo ========================================
echo.

set SETUP_OK=1

REM Check Java installation
echo [1/4] Checking Java installation...
where java >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo   ❌ Java is NOT installed or not in PATH
    echo   Please install Java JDK 17 or later
    echo   Download from: https://adoptium.net/
    set SETUP_OK=0
) else (
    echo   ✅ Java is available
    java -version 2>&1 | findstr /C:"version"
)
echo.

REM Check Maven installation
echo [2/4] Checking Maven installation...
where mvn >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo   ❌ Maven is NOT installed or not in PATH
    echo   Please install Apache Maven
    echo   Download from: https://maven.apache.org/download.cgi
    set SETUP_OK=0
) else (
    echo   ✅ Maven is available
    mvn --version 2>&1 | findstr /C:"Apache Maven"
)
echo.

REM Check project structure
echo [3/4] Checking project structure...
if not exist "pom.xml" (
    echo   ❌ pom.xml not found - not in project directory?
    set SETUP_OK=0
) else (
    echo   ✅ pom.xml found
)

if not exist "src\main\java" (
    echo   ❌ src\main\java directory not found
    set SETUP_OK=0
) else (
    echo   ✅ Source directory structure is correct
)
echo.

REM Check configuration
echo [4/4] Checking configuration...
if exist "src\main\resources\config.properties" (
    echo   ✅ config.properties found
) else (
    if exist "src\main\resources\config.properties.template" (
        echo   ⚠️  config.properties.template found but config.properties missing
        echo   You can copy the template to create your configuration:
        echo   copy src\main\resources\config.properties.template src\main\resources\config.properties
    ) else (
        echo   ❌ No configuration template found
        set SETUP_OK=0
    )
)

REM Check environment variables
if defined ABBYY_APP_ID (
    echo   ✅ ABBYY_APP_ID environment variable is set
) else (
    echo   ⚠️  ABBYY_APP_ID environment variable not set
    echo   You can set it with: set ABBYY_APP_ID=your_app_id
)

if defined ABBYY_PASSWORD (
    echo   ✅ ABBYY_PASSWORD environment variable is set
) else (
    echo   ⚠️  ABBYY_PASSWORD environment variable not set
    echo   You can set it with: set ABBYY_PASSWORD=your_password
)
echo.

REM Summary
echo ========================================
if %SETUP_OK%==1 (
    echo ✅ Environment setup is COMPLETE!
    echo.
    echo Next steps:
    echo 1. Configure your ABBYY credentials ^(see above^)
    echo 2. Run compile.bat to build the project
    echo 3. Use run-testapp.bat or run-processmany.bat to run applications
) else (
    echo ❌ Environment setup is INCOMPLETE!
    echo.
    echo Please fix the issues above before proceeding.
)
echo ========================================

pause
