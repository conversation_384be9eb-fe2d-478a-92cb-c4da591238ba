@echo off
REM ABBYY Cloud OCR SDK Java Client - TestApp Runner
REM This script runs the TestApp main program

setlocal enabledelayedexpansion

REM Check if compiled classes exist
if not exist "target\classes\TestApp.class" (
    echo ERROR: Project is not compiled yet
    echo Please run compile.bat first
    echo.
    pause
    exit /b 1
)

REM Check if Java is available
where java >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java JDK and add it to your PATH
    pause
    exit /b 1
)

REM Show help if no arguments provided
if "%~1"=="" (
    echo ========================================
    echo ABBYY Cloud OCR SDK - TestApp Runner
    echo ========================================
    echo.
    echo Usage: run-testapp.bat [mode] [arguments...]
    echo.
    echo Available modes:
    echo   recognize    - Recognize documents
    echo   busCard      - Recognize business cards
    echo   textField    - Recognize text fields
    echo   barcode      - Recognize barcodes
    echo   processFields - Process multiple fields
    echo   MRZ          - Process Machine-Readable Zones
    echo   help [mode]  - Show detailed help for a mode
    echo.
    echo Examples:
    echo   run-testapp.bat recognize image.jpg result.xml
    echo   run-testapp.bat help recognize
    echo.
    echo Configuration:
    echo   Set environment variables ABBYY_APP_ID and ABBYY_PASSWORD
    echo   Or copy config.properties.template to config.properties and edit
    echo.
    pause
    exit /b 0
)

REM Run TestApp with all arguments
echo Running TestApp with arguments: %*
echo.
java -cp target\classes TestApp %*

REM Check exit code
if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo TestApp execution failed!
    echo ========================================
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ========================================
echo TestApp execution completed successfully!
echo ========================================
